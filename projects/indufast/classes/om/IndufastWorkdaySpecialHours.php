<?php

  use classes\TimesTrait;

  AppModel::loadModelClass('IndufastWorkdaySpecialHoursModel');

  class IndufastWorkdaySpecialHours extends IndufastWorkdaySpecialHoursModel {

    use ModelFillTrait;
    use ModelTimeTrait;
    use ValidationTrait;
    use PropertyCastTrait;
    use TimesTrait;

    const string TYPE_LEAVE = 'leave';
    const string TYPE_SPECIAL_LEAVE = 'special-leave';
    const string TYPE_UNEXCUSED_LEAVE = 'unexcused-leave';
    const string TYPE_SICK = 'sick';

    protected array $fillable = [
      'workday_id' => 'required|integer|exists:indufast_workday,id|locked_month:workday_id,{workday_id}',
      'type'       => 'required|in:leave,special-leave,unexcused-leave,sick',
    ];

    const array CAST_PROPERTIES = [
      'id'         => 'int',
      'workday_id' => 'int',
      'from_db'    => 'hidden',
    ];

    public function getFillable(): array {
      $this->fillable['duration'] = [
        'required',
        'callback' => fn($v) => IndufastWorkdaySpecialHours::validTime($v),
      ];

      return $this->fillable;
    }

  }