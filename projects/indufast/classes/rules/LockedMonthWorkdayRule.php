<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class LockedMonthWorkdayRule extends Rule {

    protected string $message = "Cannot edit workdays for a locked month";
    protected array $fillableParams = [];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $workday_id = $this->attribute()->value('workday_id');
      
      if (!$workday_id || !$value) return true;

      // Query to get the workday's date and employee_id, then check if the monthly summary is locked
      $query = sprintf(
        "SELECT ws.locked 
         FROM indufast_workday wd
         JOIN indufast_workday_summary ws ON (
           ws.employee_id = wd.employee_id 
           AND ws.month = MONTH(wd.date) 
           AND ws.year = YEAR(wd.date)
         )
         WHERE wd.id = '%s'",
        $workday_id
      );
      
      $result = DBConn::db_link()->query($query);
      
      if (!$result) return true;

      $data = $result->fetch_assoc();
      
      if (!$data) return true;

      return intval($data['locked']) === 0;
    }

  }
