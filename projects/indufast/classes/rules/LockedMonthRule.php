<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class LockedMonthRule extends Rule {

    protected string $message = "Cannot edit workdays for a locked month";
    protected array $fillableParams = [];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $employee_id = $this->attribute()->value('employee_id');

      if (!$employee_id || !$value) return true;

      $month = date('m', strtotime($value));
      $year = date('Y', strtotime($value));

      // Query to check if the monthly summary for this employee/month/year is locked
      $query = sprintf(
        "SELECT locked FROM indufast_workday_summary
         WHERE employee_id = '%s'
         AND month = '%s'
         AND year = '%s'",
        $employee_id,
        $month,
        $year
      );

      $result = DBConn::db_link()->query($query);

      if (!$result) return true;

      $data = $result->fetch_assoc();

      if (!$data) return true;

      return intval($data['locked']) === 0;
    }

  }