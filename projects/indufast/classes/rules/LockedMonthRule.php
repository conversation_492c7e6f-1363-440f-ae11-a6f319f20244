<?php declare(strict_types=1);

  namespace classes\rules;

  use DBConn;
  use GsdException;
  use Somnambulist\Components\Validation\Exceptions\ParameterException;
  use Somnambulist\Components\Validation\Rule;

  class LockedMonthRule extends Rule {

    protected string $message = "Cannot edit workdays for a locked month";
    protected array $fillableParams = ['workday_id'];

    public function __construct() {
    }

    /**
     * @throws ParameterException
     * @throws GsdException
     */
    public function check($value): bool {
      $workday_id = $this->parameter('workday_id');

      if ($workday_id) {
        // If workday_id parameter is provided, get date and employee_id from the workday
        return $this->checkByWorkdayId($workday_id);
      } else {
        // If no workday_id parameter, treat $value as date and get employee_id from context
        return $this->checkByDate($value);
      }
    }

    /**
     * Check locked status using workday_id (for workday lines and special hours)
     */
    private function checkByWorkdayId($workday_id): bool {
      if (!$workday_id) {
        return true; // Skip validation if workday_id is missing
      }

      // Query to get the workday's date and employee_id, then check if the monthly summary is locked
      $query = sprintf(
        "SELECT ws.locked
         FROM indufast_workday wd
         JOIN indufast_workday_summary ws ON (
           ws.employee_id = wd.employee_id
           AND ws.month = MONTH(wd.date)
           AND ws.year = YEAR(wd.date)
         )
         WHERE wd.id = '%s'",
        $workday_id
      );

      $result = DBConn::db_link()->query($query);

      if (!$result) {
        return true; // If no summary exists yet, allow editing
      }

      $data = $result->fetch_assoc();

      if (!$data) {
        return true; // If no summary exists yet, allow editing
      }

      // Return false (validation fails) if the month is locked (locked = 1)
      return intval($data['locked']) === 0;
    }

    /**
     * Check locked status using date directly (for workdays)
     */
    private function checkByDate($date): bool {
      // Get the employee_id from the current validation context
      $employee_id = $this->attribute()->value('employee_id');

      if (!$employee_id || !$date) {
        return true; // Skip validation if required data is missing
      }

      // Extract month and year from the workday date
      $month = date('m', strtotime($date));
      $year = date('Y', strtotime($date));

      // Query to check if the monthly summary for this employee/month/year is locked
      $query = sprintf(
        "SELECT locked FROM indufast_workday_summary
         WHERE employee_id = '%s'
         AND month = '%s'
         AND year = '%s'",
        $employee_id,
        $month,
        $year
      );

      $result = DBConn::db_link()->query($query);

      if (!$result) {
        return true; // If no summary exists yet, allow editing
      }

      $data = $result->fetch_assoc();

      if (!$data) {
        return true; // If no summary exists yet, allow editing
      }

      // Return false (validation fails) if the month is locked (locked = 1)
      return intval($data['locked']) === 0;
    }

  }